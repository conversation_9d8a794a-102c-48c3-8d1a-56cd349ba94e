import boto3
import json
from decimal import Decimal
from botocore.exceptions import Client<PERSON>rror
from datetime import datetime

# --- CONFIGURATION ---
TABLE_NAME = 'EmissionReports'
REGION = 'us-east-1'  # Change if needed
PARTITION_KEY = 'reportId'

# --- INIT DYNAMODB RESOURCE ---
dynamodb = boto3.resource('dynamodb', region_name=REGION)

# --- STEP 1: CREATE TABLE ---
def create_table():
    try:
        table = dynamodb.create_table(
            TableName=TABLE_NAME,
            KeySchema=[
                {
                    'AttributeName': PARTITION_KEY,
                    'KeyType': 'HASH'  # Partition key
                }
            ],
            AttributeDefinitions=[
                {
                    'AttributeName': PARTITION_KEY,
                    'AttributeType': 'S'
                }
            ],
            ProvisionedThroughput={
                'ReadCapacityUnits': 5,
                'WriteCapacityUnits': 5
            }
        )
        print(f"Creating table '{TABLE_NAME}'...")
        table.wait_until_exists()
        print("✅ Table created successfully!")
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceInUseException':
            print(f"⚠️ Table '{TABLE_NAME}' already exists.")
        else:
            raise

# --- STEP 2: ADD SAMPLE DATA ---
def add_sample_data():
    table = dynamodb.Table(TABLE_NAME)
    
    sample_reports = [
        {
            'reportId': 'RPT-001',
            'date': '2024-01-15',
            'emissionType': 'CO2',
            'emissionValue': Decimal('125.50'),
            'reportedBy': 'John Smith',
            'unit': 'tons'
        },
        {
            'reportId': 'RPT-002',
            'date': '2024-01-16',
            'emissionType': 'CH4',
            'emissionValue': Decimal('45.25'),
            'reportedBy': 'Jane Doe',
            'unit': 'kg'
        },
        {
            'reportId': 'RPT-003',
            'date': '2024-01-17',
            'emissionType': 'N2O',
            'emissionValue': Decimal('12.75'),
            'reportedBy': 'Bob Johnson',
            'unit': 'kg'
        },
        {
            'reportId': 'RPT-004',
            'date': '2024-01-18',
            'emissionType': 'CO2',
            'emissionValue': Decimal('200.00'),
            'reportedBy': 'Alice Brown',
            'unit': 'tons'
        },
        {
            'reportId': 'RPT-005',
            'date': '2024-01-19',
            'emissionType': 'CH4',
            'emissionValue': Decimal('67.80'),
            'reportedBy': 'Charlie Wilson',
            'unit': 'kg'
        }
    ]
    
    for report in sample_reports:
        try:
            table.put_item(Item=report)
            print(f"✅ Added report: {report['reportId']}")
        except ClientError as e:
            print(f"❌ Failed to add report {report['reportId']}: {e.response['Error']['Message']}")
    
    print("✅ Sample data added successfully!")

# --- MAIN EXECUTION ---
if __name__ == '__main__':
    create_table()
    add_sample_data()

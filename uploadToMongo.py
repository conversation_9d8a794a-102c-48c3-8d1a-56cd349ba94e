import json
import requests

API_URL = 'http://localhost:3000/calibration-reports'
DATA_FILE = 'data.json'

# Load data
with open(DATA_FILE, 'r') as f:
    data = json.load(f)

# Clean and remove "id" or "_id" if exists
def clean(item):
    item = {k: (v if v != "" else None) for k, v in item.items()}
    item.pop('id', None)
    item.pop('_id', None)
    return item

# Send each item via POST
for item in data:
    cleaned = clean(item)
    response = requests.post(API_URL, json=cleaned)
    if response.status_code in [200, 201]:
        print(f"✅ Inserted: {cleaned.get('Calibration_ID')}")
    else:
        print(f"❌ Failed: {cleaned.get('Calibration_ID')} — {response.status_code} — {response.text}")

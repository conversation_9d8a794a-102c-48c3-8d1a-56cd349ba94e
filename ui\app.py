from dotenv import load_dotenv
import json
import logging
import logging.config
import os
import re
from services import bedrock_agent_runtime
import streamlit as st
import uuid
import yaml
import boto3
import pandas as pd
from botocore.exceptions import ClientError

load_dotenv()

# Configure logging using YAML
if os.path.exists("logging.yaml"):
    with open("logging.yaml", "r") as file:
        config = yaml.safe_load(file)
        logging.config.dictConfig(config)
else:
    # Python 3.10 compatible way to get log level
    log_level_name = os.environ.get("LOG_LEVEL", "INFO").upper()
    log_level = getattr(logging, log_level_name, logging.INFO)
    logging.basicConfig(level=log_level)

logger = logging.getLogger(__name__)

# Get config from environment variables
supplier_agent_id = os.environ.get("BEDROCK_AGENT_ID")
supplier_agent_alias_id = os.environ.get("BEDROCK_AGENT_ALIAS_ID", "TSTALIASID")  # TSTALIASID is the default test alias ID
emission_agent_id = "VNXHJMLGLW"
emission_agent_alias_id = "Y120OSYL9W"
ui_title = os.environ.get("BEDROCK_AGENT_TEST_UI_TITLE", "Welcome to ESG Chatbot!")
ui_icon = os.environ.get("BEDROCK_AGENT_TEST_UI_ICON")

# DynamoDB configuration
dynamodb_region = os.environ.get("AWS_REGION", "us-east-1")
emission_reports_table = "EmissionReports"


def init_session_state():
    st.session_state.supplier_session_id = str(uuid.uuid4())
    st.session_state.supplier_messages = []
    st.session_state.supplier_citations = []
    st.session_state.supplier_trace = {}

    st.session_state.emission_session_id = str(uuid.uuid4())
    st.session_state.emission_messages = []
    st.session_state.emission_citations = []
    st.session_state.emission_trace = {}


# General page configuration and initialization
st.set_page_config(page_title=ui_title, page_icon=ui_icon, layout="wide")
st.title(ui_title)
if len(st.session_state.items()) == 0:
    init_session_state()

# Create tabs
tab1, tab2, tab3 = st.tabs(["Supplier Information", "Emission Data", "Reports"])

# Function to handle chat for a specific agent
def handle_chat(agent_id, agent_alias_id, session_key, messages_key, citations_key, trace_key, chat_key):
    # Messages in the conversation
    for message in st.session_state[messages_key]:
        with st.chat_message(message["role"]):
            st.markdown(message["content"], unsafe_allow_html=True)

    # Chat input that invokes the agent
    if prompt := st.chat_input(key=chat_key):
        st.session_state[messages_key].append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.write(prompt)

        with st.chat_message("assistant"):
            with st.empty():
                with st.spinner():
                    response = bedrock_agent_runtime.invoke_agent(
                        agent_id,
                        agent_alias_id,
                        st.session_state[session_key],
                        prompt
                    )
                output_text = response["output_text"]

                # Check if the output is a JSON object with the instruction and result fields
                try:
                    # When parsing the JSON, strict mode must be disabled to handle badly escaped newlines
                    # TODO: This is still broken in some cases - AWS needs to double sescape the field contents
                    output_json = json.loads(output_text, strict=False)
                    if "instruction" in output_json and "result" in output_json:
                        output_text = output_json["result"]
                except json.JSONDecodeError as e:
                    pass

                # Add citations
                if len(response["citations"]) > 0:
                    citation_num = 1
                    output_text = re.sub(r"%\[(\d+)\]%", r"<sup>[\1]</sup>", output_text)

                    citation_locs = ""
                    for citation in response["citations"]:
                        for retrieved_ref in citation["retrievedReferences"]:
                            citation_marker = f"[{citation_num}]"
                            citation_locs += f"\n<br>{citation_marker} {retrieved_ref['location']['s3Location']['uri']}"
                            citation_num += 1
                    output_text += f"\n{citation_locs}"

                st.session_state[messages_key].append({"role": "assistant", "content": output_text})
                st.session_state[citations_key] = response["citations"]
                st.session_state[trace_key] = response["trace"]
                st.markdown(output_text, unsafe_allow_html=True)

# Function to get emission reports from DynamoDB
def get_emission_reports():
    try:
        dynamodb = boto3.resource('dynamodb', region_name=dynamodb_region)
        table = dynamodb.Table(emission_reports_table)

        response = table.scan()
        items = response['Items']

        # Handle pagination if there are more items
        while 'LastEvaluatedKey' in response:
            response = table.scan(ExclusiveStartKey=response['LastEvaluatedKey'])
            items.extend(response['Items'])

        return items
    except ClientError as e:
        st.error(f"Error fetching data from DynamoDB: {e.response['Error']['Message']}")
        return []
    except Exception as e:
        st.error(f"Unexpected error: {str(e)}")
        return []

# Tab 1: Supplier Information
with tab1:
    st.header("Supplier Information Chat")
    handle_chat(
        supplier_agent_id,
        supplier_agent_alias_id,
        "supplier_session_id",
        "supplier_messages",
        "supplier_citations",
        "supplier_trace",
        "supplier_chat"
    )

# Tab 2: Emission Data
with tab2:
    st.header("Emission Data Chat")
    handle_chat(
        emission_agent_id,
        emission_agent_alias_id,
        "emission_session_id",
        "emission_messages",
        "emission_citations",
        "emission_trace",
        "emission_chat"
    )

# Tab 3: Reports
with tab3:
    st.header("Emission Reports")

    if st.button("Refresh Data"):
        st.rerun()

    # Fetch and display emission reports
    reports = get_emission_reports()

    if reports:
        # Convert to DataFrame for better display
        df = pd.DataFrame(reports)

        # Ensure columns are in the specified order
        column_order = ['reportId', 'date', 'emissionType', 'emissionValue', 'reportedBy', 'unit']
        existing_columns = [col for col in column_order if col in df.columns]

        if existing_columns:
            df = df[existing_columns]

        st.dataframe(df, use_container_width=True)

        # Display summary statistics
        st.subheader("Summary")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total Reports", len(reports))

        with col2:
            if 'emissionType' in df.columns:
                unique_types = df['emissionType'].nunique()
                st.metric("Emission Types", unique_types)

        with col3:
            if 'emissionValue' in df.columns:
                try:
                    # Convert to numeric, handling any non-numeric values
                    numeric_values = pd.to_numeric(df['emissionValue'], errors='coerce')
                    total_emissions = numeric_values.sum()
                    st.metric("Total Emissions", f"{total_emissions:.2f}")
                except:
                    st.metric("Total Emissions", "N/A")
    else:
        st.info("No emission reports found in the database.")

# Sidebar button to reset session state
with st.sidebar:
    if st.button("Reset All Sessions"):
        init_session_state()

trace_types_map = {
    "Pre-Processing": ["preGuardrailTrace", "preProcessingTrace"],
    "Orchestration": ["orchestrationTrace"],
    "Post-Processing": ["postProcessingTrace", "postGuardrailTrace"]
}

trace_info_types_map = {
    "preProcessingTrace": ["modelInvocationInput", "modelInvocationOutput"],
    "orchestrationTrace": ["invocationInput", "modelInvocationInput", "modelInvocationOutput", "observation", "rationale"],
    "postProcessingTrace": ["modelInvocationInput", "modelInvocationOutput", "observation"]
}

# Function to display trace information
def display_trace_info(trace_data, citations_data, title_prefix=""):
    st.subheader(f"{title_prefix}Trace")

    # Show each trace type in separate sections
    step_num = 1
    for trace_type_header in trace_types_map:
        st.write(f"**{trace_type_header}**")

        # Organize traces by step similar to how it is shown in the Bedrock console
        has_trace = False
        for trace_type in trace_types_map[trace_type_header]:
            if trace_type in trace_data:
                has_trace = True
                trace_steps = {}

                for trace in trace_data[trace_type]:
                    # Each trace type and step may have different information for the end-to-end flow
                    if trace_type in trace_info_types_map:
                        trace_info_types = trace_info_types_map[trace_type]
                        for trace_info_type in trace_info_types:
                            if trace_info_type in trace:
                                trace_id = trace[trace_info_type]["traceId"]
                                if trace_id not in trace_steps:
                                    trace_steps[trace_id] = [trace]
                                else:
                                    trace_steps[trace_id].append(trace)
                                break
                    else:
                        trace_id = trace["traceId"]
                        trace_steps[trace_id] = [
                            {
                                trace_type: trace
                            }
                        ]

                # Show trace steps in JSON similar to the Bedrock console
                for trace_id in trace_steps.keys():
                    with st.expander(f"Trace Step {str(step_num)}", expanded=False):
                        for trace in trace_steps[trace_id]:
                            trace_str = json.dumps(trace, indent=2)
                            st.code(trace_str, language="json", line_numbers=True, wrap_lines=True)
                    step_num += 1
        if not has_trace:
            st.text("None")

    st.subheader(f"{title_prefix}Citations")
    if len(citations_data) > 0:
        citation_num = 1
        for citation in citations_data:
            for retrieved_ref_num, retrieved_ref in enumerate(citation["retrievedReferences"]):
                with st.expander(f"Citation [{str(citation_num)}]", expanded=False):
                    citation_str = json.dumps(
                        {
                            "generatedResponsePart": citation["generatedResponsePart"],
                            "retrievedReference": citation["retrievedReferences"][retrieved_ref_num]
                        },
                        indent=2
                    )
                    st.code(citation_str, language="json", line_numbers=True, wrap_lines=True)
                citation_num = citation_num + 1
    else:
        st.text("None")

# Sidebar section for trace and citations
with st.sidebar:
    st.title("Debug Information")

    # Create tabs for different agent traces
    trace_tab1, trace_tab2 = st.tabs(["Supplier", "Emission"])

    with trace_tab1:
        if hasattr(st.session_state, 'supplier_trace') and hasattr(st.session_state, 'supplier_citations'):
            display_trace_info(st.session_state.supplier_trace, st.session_state.supplier_citations, "Supplier ")
        else:
            st.text("No trace data available")

    with trace_tab2:
        if hasattr(st.session_state, 'emission_trace') and hasattr(st.session_state, 'emission_citations'):
            display_trace_info(st.session_state.emission_trace, st.session_state.emission_citations, "Emission ")
        else:
            st.text("No trace data available")

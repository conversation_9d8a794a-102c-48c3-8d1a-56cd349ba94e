import { Entity, model, property } from '@loopback/repository';

@model()
export class CalibrationReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      nullable: true
    }
  })
  Calibration_ID: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Dealer_Name?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Location?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Zone?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Category?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Latest_Self_assessment_Month?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  MSI_Self_assessment_Score?: string;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true
    }
  })
  MSI_Calibration_Score?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  MSI_Rating?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Updated_Score?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Updated_MSI_Rating?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Calibration_Submission_Date?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Calibration_Team_Member?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Approved_By?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Approved_Date?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true
    }
  })
  Status_of_Actions?: string;


  constructor(data?: Partial<CalibrationReport>) {
    super(data);
  }
}

export interface CalibrationReportRelations {
  // describe navigational properties here
}

export type CalibrationReportWithRelations = CalibrationReport & CalibrationReportRelations;

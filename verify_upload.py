import boto3
from botocore.exceptions import ClientError

# Configuration
TABLE_NAME = 'SupplierMSICalibrationCompletedData'
REGION = 'us-east-1'

# Initialize DynamoDB resource
dynamodb = boto3.resource('dynamodb', region_name=REGION)

def verify_upload():
    """Verify that data was uploaded successfully"""
    try:
        table = dynamodb.Table(TABLE_NAME)
        
        # Get table item count
        response = table.scan(Select='COUNT')
        item_count = response['Count']
        
        print(f"✅ Table '{TABLE_NAME}' contains {item_count} items")
        
        # Get a sample item to verify structure
        if item_count > 0:
            sample_response = table.scan(Limit=1)
            if sample_response['Items']:
                sample_item = sample_response['Items'][0]
                print(f"📋 Sample item keys: {list(sample_item.keys())}")
                print(f"📋 Sample item ID: {sample_item.get('id', 'N/A')}")
                print(f"📋 Sample Dealer Name: {sample_item.get('Actions_to_be_Reviewed_by_TVS', 'N/A')}")
        
    except ClientError as e:
        print(f"❌ Error verifying upload: {e.response['Error']['Message']}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

if __name__ == '__main__':
    verify_upload()

import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {DynamoDbDataSource} from '../datasources';
import {CalibrationReport, CalibrationReportRelations} from '../models';

export class CalibrationReportRepository extends DefaultCrudRepository<
  CalibrationReport,
  typeof CalibrationReport.prototype.id,
  CalibrationReportRelations
> {
  constructor(
    @inject('datasources.DynamoDb') dataSource: DynamoDbDataSource,
  ) {
    super(CalibrationReport, dataSource);
  }
}

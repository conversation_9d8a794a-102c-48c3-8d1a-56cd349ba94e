{"name": "excel-to-csv", "version": "0.0.1", "description": "excelToCSV", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "18 || 20 || 22"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t excel-to-csv .", "docker:run": "docker run -p 3000:3000 -d excel-to-csv", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": "adhi1 <<EMAIL>>", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@loopback/boot": "^7.0.2", "@loopback/core": "^6.0.2", "@loopback/repository": "^7.0.12", "@loopback/rest": "^14.0.2", "@loopback/rest-explorer": "^7.0.2", "@loopback/service-proxy": "^7.0.2", "loopback-connector-mongodb": "^6.3.1", "tslib": "^2.0.0"}, "devDependencies": {"@loopback/build": "^11.0.2", "@loopback/eslint-config": "^15.0.2", "@loopback/testlab": "^7.0.2", "@types/node": "^16.18.96", "eslint": "^8.57.0", "source-map-support": "^0.5.21", "typescript": "~5.2.2"}}
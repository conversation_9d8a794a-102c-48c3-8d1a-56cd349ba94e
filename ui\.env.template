# The ID of the agent.
BEDROCK_AGENT_ID='LIOC5<PERSON>HSH'

# The ID of the agent alias. The default `TSTALIASID` will be used if it is not set.
BEDROCK_AGENT_ALIAS_ID='8R9DDX1FDI'

# The page title. The default `Agents for Amazon Bedrock Test UI` will used if it is not set.
# BEDROCK_AGENT_TEST_UI_ICON=

# The favicon, such as `:bar_chart:`. The default Streamlit icon will be used if it is not set.
# BEDROCK_AGENT_TEST_UI_TITLE=

# The log level. One of: `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`.
# The default `INFO` will be used if it is not set.
# For more advanced logging configuration, use `logging.yaml` instead.
# 
# LOG_LEVEL=

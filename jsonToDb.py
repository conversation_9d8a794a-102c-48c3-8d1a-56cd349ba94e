import boto3
import json
import time
from decimal import Decimal
from botocore.exceptions import ClientError

# --- CONFIGURATION ---
TABLE_NAME = 'SupplierMSICalibrationCompletedData'
REGION = 'us-east-1'  # Change if needed
PARTITION_KEY = 'id'
DATA_FILE = 'data.json'

# --- INIT DYNAMODB RESOURCE ---
dynamodb = boto3.resource('dynamodb', region_name=REGION)

# --- STEP 1: CREATE TABLE ---
def create_table():
    try:
        table = dynamodb.create_table(
            TableName=TABLE_NAME,
            KeySchema=[
                {
                    'AttributeName': PARTITION_KEY,
                    'KeyType': 'HASH'  # Partition key
                }
            ],
            AttributeDefinitions=[
                {
                    'AttributeName': PARTITION_KEY,
                    'AttributeType': 'S'
                }
            ],
            ProvisionedThroughput={
                'ReadCapacityUnits': 5,
                'WriteCapacityUnits': 5
            }
        )
        print(f"Creating table '{TABLE_NAME}'...")
        table.wait_until_exists()
        print("✅ Table created successfully!")
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceInUseException':
            print(f"⚠️ Table '{TABLE_NAME}' already exists.")
        else:
            raise

# --- STEP 2: LOAD DATA FROM FILE ---
def load_data():
    with open(DATA_FILE, 'r') as f:
        data = json.load(f)
    return data

# --- HELPER: CONVERT FLOATS TO DECIMAL ---
def convert_floats_to_decimal(obj, key=None):
    """Recursively convert float values to Decimal for DynamoDB compatibility"""
    if isinstance(obj, float):
        return Decimal(str(obj))
    elif isinstance(obj, int) and key == PARTITION_KEY:
        # Convert partition key to string to match table schema
        return str(obj)
    elif isinstance(obj, dict):
        return {k: convert_floats_to_decimal(v, k) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_floats_to_decimal(item) for item in obj]
    else:
        return obj

# --- STEP 3: CLEAN DATA AND PUT ITEMS ---
def upload_data(data):
    table = dynamodb.Table(TABLE_NAME)
    for item in data:
        # Clean empty strings and convert floats to Decimal
        cleaned_item = {k: (v if v != "" else None) for k, v in item.items()}
        cleaned_item = convert_floats_to_decimal(cleaned_item)
        try:
            table.put_item(Item=cleaned_item)
        except ClientError as e:
            print(f"❌ Failed to upload item: {e.response['Error']['Message']}")
    print("✅ Data uploaded successfully!")

# --- MAIN EXECUTION ---
if __name__ == '__main__':
    create_table()
    data = load_data()
    upload_data(data)

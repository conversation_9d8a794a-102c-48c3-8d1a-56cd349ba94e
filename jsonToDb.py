import boto3
import json
import time
from botocore.exceptions import ClientError

# --- CONFIGURATION ---
TABLE_NAME = 'DealerMSICalibrationCompletedData'
REGION = 'us-east-1'  # Change if needed
PARTITION_KEY = 'id'
DATA_FILE = 'data.json'

# --- INIT DYNAMODB RESOURCE ---
dynamodb = boto3.resource('dynamodb', region_name=REGION)

# --- STEP 1: CREATE TABLE ---
def create_table():
    try:
        table = dynamodb.create_table(
            TableName=TABLE_NAME,
            KeySchema=[
                {
                    'AttributeName': PARTITION_KEY,
                    'KeyType': 'HASH'  # Partition key
                }
            ],
            AttributeDefinitions=[
                {
                    'AttributeName': PARTITION_KEY,
                    'AttributeType': 'S'
                }
            ],
            ProvisionedThroughput={
                'ReadCapacityUnits': 5,
                'WriteCapacityUnits': 5
            }
        )
        print(f"Creating table '{TABLE_NAME}'...")
        table.wait_until_exists()
        print("✅ Table created successfully!")
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceInUseException':
            print(f"⚠️ Table '{TABLE_NAME}' already exists.")
        else:
            raise

# --- STEP 2: LOAD DATA FROM FILE ---
def load_data():
    with open(DATA_FILE, 'r') as f:
        data = json.load(f)
    return data

# --- STEP 3: CLEAN DATA AND PUT ITEMS ---
def upload_data(data):
    table = dynamodb.Table(TABLE_NAME)
    for item in data:
        cleaned_item = {k: (v if v != "" else None) for k, v in item.items()}
        try:
            table.put_item(Item=cleaned_item)
        except ClientError as e:
            print(f"❌ Failed to upload item: {e.response['Error']['Message']}")
    print("✅ Data uploaded successfully!")

# --- MAIN EXECUTION ---
if __name__ == '__main__':
    create_table()
    data = load_data()
    upload_data(data)

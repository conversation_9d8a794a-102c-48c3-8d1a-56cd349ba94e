import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CalibrationReport} from '../models';
import {CalibrationReportRepository} from '../repositories';

export class CalibrationReportController {
  constructor(
    @repository(CalibrationReportRepository)
    public calibrationReportRepository : CalibrationReportRepository,
  ) {}

  
  @get('/calibration-reports/{id}')
  @response(200, {
    description: 'CalibrationReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(CalibrationReport, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(CalibrationReport, {exclude: 'where'}) filter?: FilterExcludingWhere<CalibrationReport>
  ): Promise<CalibrationReport> {
    return this.calibrationReportRepository.findById(id, filter);
  }

  
}
